/*
 * ************************************************************************
 *  build.gradle
 * *************************************************************************
 * Copyright © 2020 VLC authors and VideoLAN
 * Author: <PERSON>Y
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston MA 02110-1301, USA.
 * **************************************************************************
 *
 *
 */

apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'

android {

    defaultConfig {
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
        compileSdk rootProject.ext.compileSdkVersion

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles 'consumer-rules.pro'
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
        vlcBundle {
            initWith release
            matchingFallbacks = ['release']
        }
    }
    namespace 'org.videolan.liveplotgraph'
    buildFeatures {
        buildConfig true
    }

}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
    implementation "androidx.appcompat:appcompat:$rootProject.ext.appCompatVersion"
    implementation "androidx.core:core-ktx:$rootProject.ext.androidxCoreVersion"
    implementation project(':application:tools')
    implementation "androidx.constraintlayout:constraintlayout:$rootProject.ext.constraintLayoutVersion"

    testImplementation "junit:junit:$rootProject.ext.junitVersion"
    androidTestImplementation "androidx.test.ext:junit:$rootProject.ext.junitExtVersion"
    androidTestImplementation "androidx.test.espresso:espresso-core:$rootProject.ext.espressoVersion"
}
