<?xml version="1.0" encoding="UTF-8" standalone="no"?><!-- Created with Inkscape (http://www.inkscape.org/) -->

<svg xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#"
    xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:svg="http://www.w3.org/2000/svg"
    xmlns="http://www.w3.org/2000/svg"
    xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
    xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape" width="24" height="24" id="svg4682"
    version="1.1" inkscape:version="0.92.4 (33fec40, 2019-01-16)"
    sodipodi:docname="ic_tv_list_play.svg"
    inkscape:export-filename="/home/<USER>/Dev/android/Icons/Test 1/ic_play_normal.png"
    inkscape:export-xdpi="90" inkscape:export-ydpi="90">
    <defs id="defs4684" />
    <sodipodi:namedview id="base" pagecolor="#ffffff" bordercolor="#666666" borderopacity="1.0"
        inkscape:pageopacity="0.0" inkscape:pageshadow="2" inkscape:zoom="26.280704"
        inkscape:cx="12.285765" inkscape:cy="9.5033488" inkscape:document-units="px"
        inkscape:current-layer="layer1" showgrid="true" inkscape:window-width="1675"
        inkscape:window-height="960" inkscape:window-x="1935" inkscape:window-y="271"
        inkscape:window-maximized="0" fit-margin-top="4" fit-margin-left="4" fit-margin-right="4"
        fit-margin-bottom="4" inkscape:snap-bbox="true" inkscape:bbox-paths="true"
        inkscape:bbox-nodes="true" inkscape:snap-bbox-edge-midpoints="true"
        inkscape:snap-bbox-midpoints="true" inkscape:object-paths="true"
        inkscape:snap-intersection-paths="true" inkscape:snap-smooth-nodes="true"
        inkscape:snap-midpoints="true" inkscape:snap-object-midpoints="true"
        inkscape:snap-center="true">
        <inkscape:grid type="xygrid" id="grid3002" originx="0" originy="0" />
    </sodipodi:namedview>
    <metadata id="metadata4687">
        <rdf:RDF>
            <cc:Work rdf:about="">
                <dc:format>image/svg+xml</dc:format>
                <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage" />
                <dc:title />
            </cc:Work>
        </rdf:RDF>
    </metadata>
    <g inkscape:label="Layer 1" inkscape:groupmode="layer" id="layer1"
        transform="translate(0,-1028.3622)">
        <path
            style="color:#000000;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;font-size:medium;line-height:normal;font-family:sans-serif;font-variant-ligatures:normal;font-variant-position:normal;font-variant-caps:normal;font-variant-numeric:normal;font-variant-alternates:normal;font-feature-settings:normal;text-indent:0;text-align:start;text-decoration:none;text-decoration-line:none;text-decoration-style:solid;text-decoration-color:#000000;letter-spacing:normal;word-spacing:normal;text-transform:none;writing-mode:lr-tb;direction:ltr;text-orientation:mixed;dominant-baseline:auto;baseline-shift:baseline;text-anchor:start;white-space:normal;shape-padding:0;clip-rule:nonzero;display:inline;overflow:visible;visibility:visible;opacity:1;isolation:auto;mix-blend-mode:normal;color-interpolation:sRGB;color-interpolation-filters:linearRGB;solid-color:#000000;solid-opacity:1;vector-effect:none;fill:#ffffff;fill-opacity:1;fill-rule:evenodd;stroke:none;stroke-width:1.59978354;stroke-linecap:butt;stroke-linejoin:round;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;color-rendering:auto;image-rendering:auto;shape-rendering:auto;text-rendering:auto;enable-background:accumulate"
            d="m 7.9527705,1032.3636 c -0.4221151,0.025 -0.7513522,0.3754 -0.7514616,0.7984 v 14.398 c -7.704e-4,0.6218 0.6771795,1.0067 1.210775,0.6874 l 11.9983891,-7.2005 c 0.517625,-0.3108 0.517625,-1.0611 0,-1.3718 l -11.9983891,-7.199 c -0.138273,-0.082 -0.2983739,-0.1222 -0.4593134,-0.1125 z"
            id="path7835-5" inkscape:connector-curvature="0" sodipodi:nodetypes="cccccccc" />
    </g>
</svg>
