<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!-- Created with Inkscape (http://www.inkscape.org/) -->

<svg
   xmlns:dc="http://purl.org/dc/elements/1.1/"
   xmlns:cc="http://creativecommons.org/ns#"
   xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
   xmlns:svg="http://www.w3.org/2000/svg"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   width="24"
   height="24"
   id="svg4682"
   version="1.1"
   inkscape:version="0.92.3 (unknown)"
   sodipodi:docname="ic_tv_list_playnext.svg"
   inkscape:export-filename="/home/<USER>/Dev/android/Icons/Test 1/ic_play_normal.png"
   inkscape:export-xdpi="90"
   inkscape:export-ydpi="90">
  <defs
     id="defs4684" />
  <sodipodi:namedview
     id="base"
     pagecolor="#ffffff"
     bordercolor="#666666"
     borderopacity="1.0"
     inkscape:pageopacity="0.0"
     inkscape:pageshadow="2"
     inkscape:zoom="15.839192"
     inkscape:cx="10.913739"
     inkscape:cy="9.6066303"
     inkscape:document-units="px"
     inkscape:current-layer="layer1"
     showgrid="true"
     inkscape:window-width="960"
     inkscape:window-height="1043"
     inkscape:window-x="2880"
     inkscape:window-y="0"
     inkscape:window-maximized="0">
    <inkscape:grid
       type="xygrid"
       id="grid3956"
       empspacing="4"
       visible="true"
       enabled="true"
       snapvisiblegridlinesonly="true" />
  </sodipodi:namedview>
  <metadata
     id="metadata4687">
    <rdf:RDF>
      <cc:Work
         rdf:about="">
        <dc:format>image/svg+xml</dc:format>
        <dc:type
           rdf:resource="http://purl.org/dc/dcmitype/StillImage" />
        <dc:title></dc:title>
      </cc:Work>
    </rdf:RDF>
  </metadata>
  <g
     inkscape:label="Layer 1"
     inkscape:groupmode="layer"
     id="layer1"
     transform="translate(0,-1028.3622)">
    <path
       style="color:#000000;display:inline;overflow:visible;visibility:visible;opacity:1;fill:#212121;fill-opacity:1;fill-rule:nonzero;stroke:none;stroke-width:10;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;marker:none;enable-background:accumulate"
       d="M 4.9882812 4.0019531 C 4.4413794 4.0119531 4.0011103 4.4531 4 5 L 4 13.001953 C 4.0001961 13.800553 4.8901455 14.276784 5.5546875 13.833984 L 11.554688 9.8320312 C 12.148312 9.4361313 12.148313 8.5638687 11.554688 8.1679688 L 5.5546875 4.1679688 C 5.3868846 4.0568687 5.1894839 3.9990531 4.9882812 4.0019531 z M 16 12 C 15.446 12 15 12.446 15 13 L 15 15 L 13 15 C 12.446 15 12 15.446 12 16 C 12 16.554 12.446 17 13 17 L 15 17 L 15 19 C 15 19.554 15.446 20 16 20 C 16.554 20 17 19.554 17 19 L 17 17 L 19 17 C 19.554 17 20 16.554 20 16 C 20 15.446 19.554 15 19 15 L 17 15 L 17 13 C 17 12.446 16.554 12 16 12 z "
       transform="translate(0,1028.3622)"
       id="path6407" />
  </g>
</svg>
