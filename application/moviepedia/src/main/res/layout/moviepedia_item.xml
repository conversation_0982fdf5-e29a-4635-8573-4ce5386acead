<?xml version="1.0" encoding="utf-8"?><!--
  ~ *************************************************************************
  ~  next_item.xml
  ~ **************************************************************************
  ~ Copyright © 2019 VLC authors and VideoLAN
  ~ Author: <PERSON>UY
  ~ This program is free software; you can redistribute it and/or modify
  ~ it under the terms of the GNU General Public License as published by
  ~ the Free Software Foundation; either version 2 of the License, or
  ~ (at your option) any later version.
  ~
  ~ This program is distributed in the hope that it will be useful,
  ~ but WITHOUT ANY WARRANTY; without even the implied warranty of
  ~ MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
  ~ GNU General Public License for more details.
  ~
  ~ You should have received a copy of the GNU General Public License
  ~ along with this program; if not, write to the Free Software
  ~ Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston MA 02110-1301, USA.
  ~ ***************************************************************************
  ~
  ~
  -->

<layout xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        xmlns:android="http://schemas.android.com/apk/res/android">

    <data>

        <import type="android.view.View" />

        <variable
                name="item"
                type="org.videolan.moviepedia.models.resolver.ResolverMedia" />

        <variable
                name="handler"
                type="org.videolan.moviepedia.ui.MediaScrapingActivity.ClickHandler" />

        <variable
                name="bgColor"
                type="int" />

        <variable
                name="imageUrl"
                type="android.net.Uri" />

        <variable
                name="cover"
                type="android.graphics.drawable.BitmapDrawable" />

    </data>

    <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="8dp"
            android:layout_marginRight="8dp"
            android:layout_marginBottom="8dp">

        <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@{bgColor}"
                android:clickable="true"
                android:focusable="true"
                android:onClick="@{() -> handler.onItemClick(item)}">

            <ImageView
                    android:id="@+id/item_image"
                    app:imageUri="@{imageUrl}"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:scaleType="fitCenter"
                    android:src="@{cover}"
                    android:background="@drawable/ic_movie_placeholder"
                    app:layout_constraintDimensionRatio="0.666666"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:srcCompat="@tools:sample/avatars" />

            <TextView
                    android:id="@+id/item_title"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:layout_marginTop="8dp"
                    android:layout_marginEnd="8dp"
                    android:text="@{item.title()}"
                    android:textColor="?attr/list_title"
                    android:textSize="16sp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/item_image"
                    tools:text="Fight Club" />

            <TextView
                    android:id="@+id/item_description"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:layout_marginEnd="8dp"
                    android:layout_marginBottom="16dp"
                    app:year="@{item}"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/item_title"
                    tools:text="1999" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.cardview.widget.CardView>
</layout>