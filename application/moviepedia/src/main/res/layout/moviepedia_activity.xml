<?xml version="1.0" encoding="utf-8"?><!--
  ~ *************************************************************************
  ~  next_activity.xml
  ~ **************************************************************************
  ~ Copyright © 2019 VLC authors and VideoLAN
  ~ Author: <PERSON>EPUY
  ~ This program is free software; you can redistribute it and/or modify
  ~ it under the terms of the GNU General Public License as published by
  ~ the Free Software Foundation; either version 2 of the License, or
  ~ (at your option) any later version.
  ~
  ~ This program is distributed in the hope that it will be useful,
  ~ but WITHOUT ANY WARRANTY; without even the implied warranty of
  ~ MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
  ~ GNU General Public License for more details.
  ~
  ~ You should have received a copy of the GNU General Public License
  ~ along with this program; if not, write to the Free Software
  ~ Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston MA 02110-1301, USA.
  ~ ***************************************************************************
  ~
  ~
  -->

<layout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <import type="android.view.View" />

        <import type="org.videolan.medialibrary.Tools" />

        <variable
                name="handler"
                type="org.videolan.moviepedia.ui.MediaScrapingActivity.ClickHandler" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

        <ImageView
                android:id="@+id/imageView8"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_gravity="center_vertical"
                android:onClick="@{handler::onBack}"
                android:scaleType="center"
                android:src="@drawable/ic_arrow_back"
                app:layout_constraintBottom_toBottomOf="@+id/search_edit_layout"
                app:layout_constraintEnd_toStartOf="@+id/search_edit_layout"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@+id/search_edit_layout" />

        <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/search_edit_layout"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:layout_marginEnd="16dp"
                android:layout_weight="1"
                app:endIconMode="clear_text"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toEndOf="@+id/imageView8"
                app:layout_constraintTop_toTopOf="parent">

            <EditText
                    android:id="@+id/search_edit_text"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:hint="@string/moviepedia_hint"
                    android:imeOptions="actionSearch"
                    android:inputType="textFilter" />
        </com.google.android.material.textfield.TextInputLayout>

        <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/next_results"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:paddingTop="8dp"
                android:clipToPadding="false"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/search_edit_layout" />

        <!--todo-->
        <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/search_no_result"
                android:visibility="gone"
                android:textAppearance="@style/Result.Title"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
