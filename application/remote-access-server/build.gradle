plugins {
    id 'com.android.library'
    id 'org.jetbrains.kotlin.android'
}

android {
    namespace 'org.videolan.vlc.remoteaccessserver'

    dataBinding {
        enabled = true
    }

    defaultConfig {
        multiDexEnabled true
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
        compileSdk rootProject.ext.compileSdkVersion

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
        buildConfigField 'Boolean', 'VLC_REMOTE_ACCESS_DEBUG', project.hasProperty("vlc_remote_access_debug") ? vlc_remote_access_debug : "false"

    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
        dev {
            initWith debug
            matchingFallbacks = ['debug']
        }
        vlcBundle {
            initWith release
            matchingFallbacks = ['release']
        }
    }

    sourceSets.main {
        assets.srcDirs = ['assets']
    }

    sourceSets.main {
        assets.srcDirs = ['assets']
    }

    compileOptions {
        coreLibraryDesugaringEnabled true
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = '1.8'
    }
    buildFeatures {
        buildConfig true
    }

}

dependencies {
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.0.4'
    devImplementation project(':application:remote-access-client')
    debugImplementation project(':application:remote-access-client')
    releaseImplementation "org.videolan.android:remote-access:$rootProject.ext.remoteAccessVersion"
    vlcBundleImplementation "org.videolan.android:remote-access:$rootProject.ext.remoteAccessVersion"
    implementation project(':application:vlc-android')
    implementation "io.ktor:ktor:$rootProject.ext.ktorVersion"
    implementation "io.ktor:ktor-server-netty:$rootProject.ext.ktorVersion"
    implementation "io.ktor:ktor-server-websockets:$rootProject.ext.ktorVersion"
    implementation "io.ktor:ktor-server-caching-headers:$rootProject.ext.ktorVersion"
    implementation "io.ktor:ktor-server-cors:$rootProject.ext.ktorVersion"
    implementation("io.ktor:ktor-server-auth:$rootProject.ext.ktorVersion")
    implementation("io.ktor:ktor-server-partial-content:$rootProject.ext.ktorVersion")
    implementation("io.ktor:ktor-network-tls-certificates:$rootProject.ext.ktorVersion")
    implementation 'org.bouncycastle:bcpkix-jdk18on:1.79'
    implementation("io.ktor:ktor-server-call-logging:$rootProject.ext.ktorVersion")
    implementation("io.ktor:ktor-server-sessions:$rootProject.ext.ktorVersion")
    debugImplementation "org.slf4j:slf4j-android:1.7.36"

    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
}