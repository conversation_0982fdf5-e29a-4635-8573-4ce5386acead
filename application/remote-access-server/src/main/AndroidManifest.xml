<?xml version="1.0" encoding="utf-8"?><!--
  ~ *************************************************************************
  ~  AndroidManifest.xml
  ~ **************************************************************************
  ~ Copyright © 2022 VLC authors and VideoLAN
  ~ Author: <PERSON>
  ~ This program is free software; you can redistribute it and/or modify
  ~ it under the terms of the GNU General Public License as published by
  ~ the Free Software Foundation; either version 2 of the License, or
  ~ (at your option) any later version.
  ~
  ~ This program is distributed in the hope that it will be useful,
  ~ but WITHOUT ANY WARRANTY; without even the implied warranty of
  ~ MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
  ~ GNU General Public License for more details.
  ~
  ~ You should have received a copy of the GNU General Public License
  ~ along with this program; if not, write to the Free Software
  ~ Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston MA 02110-1301, USA.
  ~ ***************************************************************************
  ~
  ~
  -->

<manifest xmlns:android="http://schemas.android.com/apk/res/android" xmlns:tools="http://schemas.android.com/tools">
    <uses-sdk tools:overrideLibrary="androidx.security"/>
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_SPECIAL_USE" />

    <application>
        <service
                android:name=".RemoteAccessService"
                android:exported="true"
                android:foregroundServiceType="specialUse">
            <property
                    android:name="android.app.PROPERTY_SPECIAL_USE_FGS_SUBTYPE"
                    android:value="HTTP server" />
        </service>
        <activity
                android:name=".gui.remoteaccess.RemoteAccessShareActivity"
                android:theme="@style/Theme.VLC"/>
        <activity
                android:name=".gui.remoteaccess.onboarding.RemoteAccessOnboardingActivity"
                android:theme="@style/Theme.VLC.Onboarding"/>
    </application>

</manifest>