<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ *************************************************************************
  ~  remote_access_onboarding_ssl.xml
  ~ **************************************************************************
  ~ Copyright © 2021 VLC authors and VideoLAN
  ~ Author: <PERSON>UY
  ~ This program is free software; you can redistribute it and/or modify
  ~ it under the terms of the GNU General Public License as published by
  ~ the Free Software Foundation; either version 2 of the License, or
  ~ (at your option) any later version.
  ~
  ~ This program is distributed in the hope that it will be useful,
  ~ but WITHOUT ANY WARRANTY; without even the implied warranty of
  ~ MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
  ~ GNU General Public License for more details.
  ~
  ~ You should have received a copy of the GNU General Public License
  ~ along with this program; if not, write to the Free Software
  ~ Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston MA 02110-1301, USA.
  ~ ***************************************************************************
  ~
  ~
  -->

<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        tools:background="@color/onboarding_grey">

    <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline15"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintGuide_percent="0.5" />

    <TextView
            android:id="@+id/welcome_title"
            android:layout_width="426dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:layout_marginBottom="8dp"
            android:text="@string/ra_onboarding_ssl_title"
            android:textAlignment="center"
            android:textAppearance="@style/VLC.Onboarding.Title"
            app:layout_constraintBottom_toTopOf="@+id/textView"
            app:layout_constraintEnd_toStartOf="@+id/guideline15"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_chainStyle="packed" />

    <TextView
            android:id="@+id/textView"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="8dp"
            android:layout_marginEnd="16dp"
            android:text="@string/ra_onboarding_ssl_desc"
            android:textAlignment="center"
            android:textAppearance="@style/VLC.Onboarding.Text"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/guideline15"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/welcome_title" />


    <ImageView
            android:id="@+id/play_pause"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            app:layout_constraintBottom_toTopOf="@+id/browser_link"
            app:layout_constraintEnd_toStartOf="@+id/deviceImage"
            app:layout_constraintStart_toEndOf="@+id/imageView20"
            app:srcCompat="@drawable/ic_remote_access_onboarding_encryption" />

    <ImageView
            android:id="@+id/imageView20"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="@+id/guideline15"
            app:layout_constraintTop_toTopOf="parent"
            app:srcCompat="@drawable/ic_browser" />

    <View
            android:id="@+id/browser_link"
            android:layout_width="0dp"
            android:layout_height="2dp"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:background="?attr/colorPrimary"
            app:layout_constraintBottom_toBottomOf="@+id/imageView20"
            app:layout_constraintEnd_toStartOf="@+id/deviceImage"
            app:layout_constraintStart_toEndOf="@+id/imageView20"
            app:layout_constraintTop_toTopOf="@+id/imageView20" />

    <ImageView
            android:id="@+id/deviceImage"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="16dp"
            app:layout_constraintBottom_toBottomOf="@+id/imageView20"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/imageView20"
            app:srcCompat="@drawable/ic_smartphone" />

    <TextView
            android:id="@+id/data"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:fontFamily="sans-serif-medium"
            android:textColor="@color/white"
            android:textSize="16sp"
            app:layout_constraintEnd_toStartOf="@+id/deviceImage"
            app:layout_constraintStart_toEndOf="@+id/imageView20"
            app:layout_constraintTop_toBottomOf="@+id/browser_link"
            tools:text="0110010" />
</androidx.constraintlayout.widget.ConstraintLayout>