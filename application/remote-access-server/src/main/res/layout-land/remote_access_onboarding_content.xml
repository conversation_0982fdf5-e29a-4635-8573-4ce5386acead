<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ *************************************************************************
  ~  onboarding_welcome.xml
  ~ **************************************************************************
  ~ Copyright © 2021 VLC authors and VideoLAN
  ~ Author: <PERSON>
  ~ This program is free software; you can redistribute it and/or modify
  ~ it under the terms of the GNU General Public License as published by
  ~ the Free Software Foundation; either version 2 of the License, or
  ~ (at your option) any later version.
  ~
  ~ This program is distributed in the hope that it will be useful,
  ~ but WITHOUT ANY WARRANTY; without even the implied warranty of
  ~ MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
  ~ GNU General Public License for more details.
  ~
  ~ You should have received a copy of the GNU General Public License
  ~ along with this program; if not, write to the Free Software
  ~ Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston MA 02110-1301, USA.
  ~ ***************************************************************************
  ~
  ~
  -->

<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        tools:background="@color/onboarding_grey">

    <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline15"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintGuide_percent="0.5" />

    <TextView
            android:id="@+id/welcome_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:layout_marginBottom="8dp"
            android:text="@string/ra_onboarding_content_title"
            android:textAlignment="center"
            android:textAppearance="@style/VLC.Onboarding.Title"
            app:layout_constraintBottom_toTopOf="@+id/textView"
            app:layout_constraintEnd_toStartOf="@+id/guideline15"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_chainStyle="packed" />

    <TextView
            android:id="@+id/textView"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="8dp"
            android:layout_marginEnd="16dp"
            android:text="@string/ra_onboarding_content_desc"
            android:textAlignment="center"
            android:textAppearance="@style/VLC.Onboarding.Text"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/guideline15"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/welcome_title" />


    <LinearLayout
            android:id="@+id/medialibrary"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:orientation="vertical"
            app:layout_constraintBottom_toTopOf="@+id/files"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="@+id/guideline15"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_chainStyle="packed"
            tools:alpha="1">

        <ImageView
                android:layout_width="64dp"
                android:layout_height="64dp"
                android:background="@drawable/round_white_transparent"
                android:padding="8dp"
                app:srcCompat="@drawable/ic_onboarding_scan" />

        <TextView
                android:id="@+id/textView37"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="@string/ra_onboarding_content_ml"
                android:textColor="@color/white" />
    </LinearLayout>

    <LinearLayout
            android:id="@+id/files"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="48dp"
            android:gravity="center_horizontal"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/playback"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="@+id/guideline15"
            app:layout_constraintTop_toBottomOf="@+id/medialibrary"
            tools:alpha="1">

        <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/round_white_transparent"
                android:padding="8dp"
                app:srcCompat="@drawable/ic_folder"
                app:tint="@color/orange500" />

        <TextView
                android:id="@+id/textView38"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="@string/ra_onboarding_content_files"
                android:textColor="@color/white" />
    </LinearLayout>

    <LinearLayout
            android:id="@+id/playback"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:orientation="vertical"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toEndOf="@+id/files"
            app:layout_constraintTop_toTopOf="@+id/files"
            tools:alpha="1">

        <FrameLayout
                android:layout_width="64dp"
                android:layout_height="64dp"
                android:background="@drawable/round_white_transparent">

            <org.videolan.vlc.gui.view.MiniVisualizer
                    android:id="@+id/vizu"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:layout_gravity="bottom|center_horizontal"
                    app:bar_color="@color/orange500" />

            <View
                    android:id="@+id/imageView22"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@drawable/ic_inverted_round" />
        </FrameLayout>

        <TextView
                android:id="@+id/textView39"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="@string/ra_onboarding_content_playback"
                android:textColor="@color/white" />
    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>