<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        tools:background="@color/onboarding_grey">

    <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            app:layout_constraintGuide_percent="0.25" />

    <TextView
            android:id="@+id/welcome_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:text="@string/ra_onboarding_how_title"
            android:textAlignment="center"
            android:textAppearance="@style/VLC.Onboarding.Title"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/guideline" />

    <TextView
            android:id="@+id/textView"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="24dp"
            android:layout_marginTop="8dp"
            android:layout_marginEnd="24dp"
            android:text="@string/ra_onboarding_how_desc"
            android:textAlignment="center"
            android:textAppearance="@style/VLC.Onboarding.Text"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/welcome_title" />



    <ImageView
            android:id="@+id/play_pause"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:alpha="0"
            app:layout_constraintBottom_toTopOf="@+id/browser_link"
            app:layout_constraintEnd_toStartOf="@+id/deviceImage"
            app:layout_constraintStart_toEndOf="@+id/imageView20"
            app:srcCompat="@drawable/ic_remote_access_onboarding_play" />

    <ImageView
            android:id="@+id/imageView20"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="32dp"
            android:layout_marginTop="72dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/textView"
            app:srcCompat="@drawable/ic_browser" />

    <View
            android:id="@+id/browser_link"
            android:layout_width="0dp"
            android:layout_height="2dp"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:background="?attr/colorPrimary"
            android:scaleX="0"
            app:layout_constraintBottom_toBottomOf="@+id/imageView20"
            app:layout_constraintEnd_toStartOf="@+id/deviceImage"
            app:layout_constraintStart_toEndOf="@+id/imageView20"
            app:layout_constraintTop_toTopOf="@+id/imageView20" />

    <org.videolan.vlc.gui.view.MiniVisualizer
            android:id="@+id/vizu"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginBottom="4dp"
            app:bar_color="@color/white"
            app:layout_constraintBottom_toBottomOf="@+id/deviceImage"
            app:layout_constraintEnd_toEndOf="@+id/deviceImage"
            app:layout_constraintStart_toStartOf="@+id/deviceImage" />

    <ImageView
            android:id="@+id/deviceImage"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="32dp"
            app:layout_constraintBottom_toBottomOf="@+id/imageView20"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/imageView20"
            app:srcCompat="@drawable/ic_smartphone" />


</androidx.constraintlayout.widget.ConstraintLayout>