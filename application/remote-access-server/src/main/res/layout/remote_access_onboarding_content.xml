<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        tools:background="@color/onboarding_grey">

    <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            app:layout_constraintGuide_percent="0.25" />

    <TextView
            android:id="@+id/welcome_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:text="@string/ra_onboarding_content_title"
            android:textAlignment="center"
            android:textAppearance="@style/VLC.Onboarding.Title"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/guideline" />

    <TextView
            android:id="@+id/textView"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="24dp"
            android:layout_marginTop="8dp"
            android:layout_marginEnd="24dp"
            android:text="@string/ra_onboarding_content_desc"
            android:textAlignment="center"
            android:textAppearance="@style/VLC.Onboarding.Text"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/welcome_title" />


    <LinearLayout
            android:id="@+id/medialibrary"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="64dp"
            android:gravity="center_horizontal"
            android:orientation="vertical"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/textView"
            tools:alpha="1">

        <ImageView
                android:layout_width="64dp"
                android:layout_height="64dp"
                android:background="@drawable/round_white_transparent"
                android:padding="8dp"
                app:srcCompat="@drawable/ic_onboarding_scan" />

        <TextView
                android:id="@+id/textView37"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="@string/ra_onboarding_content_ml"
                android:textColor="@color/white" />
    </LinearLayout>

    <LinearLayout
            android:id="@+id/files"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="48dp"
            android:gravity="center_horizontal"
            android:orientation="vertical"
            app:layout_constraintEnd_toStartOf="@+id/playback"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/medialibrary"
            tools:alpha="1">

        <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/round_white_transparent"
                android:padding="8dp"
                app:srcCompat="@drawable/ic_folder"
                app:tint="@color/orange500" />

        <TextView
                android:id="@+id/textView38"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="@string/ra_onboarding_content_files"
                android:textColor="@color/white" />
    </LinearLayout>

    <LinearLayout
            android:id="@+id/playback"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="48dp"
            android:gravity="center_horizontal"
            android:orientation="vertical"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toEndOf="@+id/files"
            app:layout_constraintTop_toBottomOf="@+id/medialibrary"
            tools:alpha="1">

        <FrameLayout
                android:layout_width="64dp"
                android:layout_height="64dp"
                android:background="@drawable/round_white_transparent">

            <org.videolan.vlc.gui.view.MiniVisualizer
                    android:id="@+id/vizu"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:layout_gravity="bottom|center_horizontal"
                    app:bar_color="@color/orange500" />

            <View
                    android:id="@+id/imageView22"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@drawable/ic_inverted_round"/>
        </FrameLayout>

        <TextView
                android:id="@+id/textView39"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="@string/ra_onboarding_content_playback"
                android:textColor="@color/white" />
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>