/*
 * ************************************************************************
 *  build.gradle
 * *************************************************************************
 * Copyright © 2020 VLC authors and VideoLAN
 * Author: <PERSON>Y
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston MA 02110-1301, USA.
 * **************************************************************************
 *
 *
 */

apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'

android {

    dataBinding {
        enabled = true
    }
    packagingOptions {
        jniLibs {
            pickFirsts += ['**/*.so']
        }
    }


    defaultConfig {
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
        compileSdk rootProject.ext.compileSdkVersion

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles 'consumer-rules.pro'
        multiDexEnabled true
        buildConfigField "String", "PUBLIC_API_KEY", "\"${getPublicApiKey(project)}\""
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
        debug {
            jniDebuggable true
        }
        dev {
            initWith debug
            matchingFallbacks = ['debug']
        }
        vlcBundle {
            initWith release
            matchingFallbacks = ['release']
        }
    }
    namespace 'org.videolan.vlc.donations'
    buildFeatures {
        aidl true
        buildConfig true
    }
}

static def getPublicApiKey(project) {
   return System.getenv('K8S_SECRET_VLC_PLAYSTORE_PUBLIC_API_KEY') ?:  project.properties['vlc_playstore_public_api_key'] ?: ""
}

dependencies {
    implementation fileTree(dir: "libs", include: ["*.jar"])
    implementation project(':application:tools')

    implementation "androidx.core:core-ktx:$rootProject.ext.androidxCoreVersion"
    implementation "androidx.appcompat:appcompat:$rootProject.ext.appCompatVersion"
    testImplementation "junit:junit:$rootProject.ext.junitVersion"
    androidTestImplementation "androidx.test.ext:junit:$rootProject.ext.junitExtVersion"
    androidTestImplementation "androidx.test.espresso:espresso-core:$rootProject.ext.espressoVersion"

}