/*
 * ************************************************************************
 *  build.gradle
 * *************************************************************************
 * Copyright © 2020 VLC authors and VideoLAN
 * Author: <PERSON>Y
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston MA 02110-1301, USA.
 * **************************************************************************
 *
 *
 */

apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-kapt'

android {

    packagingOptions {
        jniLibs {
            pickFirsts += ['**/*.so']
        }
    }


    defaultConfig {
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
        compileSdk rootProject.ext.compileSdkVersion

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles 'consumer-rules.pro'
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
        dev {
            initWith debug
            matchingFallbacks = ['debug']
        }
        vlcBundle {
            initWith release
            matchingFallbacks = ['release']
        }
    }
    namespace 'org.videolan.vlc.mediadb'

}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])

    implementation "androidx.appcompat:appcompat:$rootProject.ext.appCompatVersion"
    implementation "androidx.core:core-ktx:$rootProject.ext.androidxCoreVersion"
    testImplementation "junit:junit:$rootProject.ext.junitVersion"
    androidTestImplementation "androidx.test.ext:junit:$rootProject.ext.junitExtVersion"
    androidTestImplementation "androidx.test.espresso:espresso-core:$rootProject.ext.espressoVersion"
    //Room
    implementation "androidx.room:room-ktx:$rootProject.ext.roomVersion"
    kapt ('org.xerial:sqlite-jdbc:3.36.0')
    kapt "androidx.room:room-compiler:$rootProject.ext.roomVersion"
    implementation project(':application:tools')
    implementation project(':application:resources')


    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-core:$rootProject.ext.kotlinx_version"
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-android:$rootProject.ext.kotlinx_version"
}
