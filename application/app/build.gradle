apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'

android {
    compileOptions {
        coreLibraryDesugaringEnabled true
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    packagingOptions {
        jniLibs {
            pickFirsts += ['lib/armeabi-v7a/libc++_shared.so', 'lib/armeabi/libc++_shared.so', 'lib/arm64-v8a/libc++_shared.so', 'lib/x86/libc++_shared.so', 'lib/x86_64/libc++_shared.so']
        }
        resources {
            excludes += ['META-INF/*']
        }
    }

    packaging {
        resources.excludes.add("META-INF/versions/9/OSGI-INF/MANIFEST.MF")
    }

    dataBinding {
        enabled = true
    }

    flavorDimensions "abi"

    defaultConfig {
        applicationId rootProject.ext.appId
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
        compileSdk rootProject.ext.compileSdkVersion
        versionCode rootProject.ext.versionCode
        versionName rootProject.ext.versionName
        vectorDrawables.useSupportLibrary = true
        multiDexEnabled true

        testInstrumentationRunner "org.videolan.vlc.MultidexTestRunner"
        // The following argument makes the Android Test Orchestrator run its
        // "pm clear" command after each test invocation. This command ensures
        // that the app's state is completely cleared between tests.
        testInstrumentationRunnerArguments clearPackageData: 'true'
    }

    signingConfigs {
        release {
            /*
            To set this properties, create file gradle.properties with these 3 props.
            e.g.
            keyStoreFile=/home/<USER>/.android/debug.keystore
            storealias=androiddebugkey
            storepwd=android
             */
            if (project.hasProperty('keyStoreFile')) {
                storeFile file(project.findProperty('keyStoreFile'))
                keyAlias project.findProperty('storealias')
                if (System.getenv('PASSWORD_KEYSTORE') != null && !System.getenv('PASSWORD_KEYSTORE').isEmpty()) {
                    storePassword = System.getenv('PASSWORD_KEYSTORE')
                    keyPassword = System.getenv('PASSWORD_KEYSTORE')
                } else {
                    storePassword project.findProperty('storepwd')
                    keyPassword project.findProperty('storepwd')
                }
            }
        }
    }
    androidComponents {
        beforeVariants(selector().withBuildType("vlcBundle")) { variantBuilder ->
            variantBuilder.minSdk = 30
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            kotlinOptions.freeCompilerArgs = ['-Xno-param-assertions']
            defaultConfig.minSdkVersion rootProject.ext.minSdkVersion
        }
        signedRelease {
            initWith release
            signingConfig = signingConfigs.release
            matchingFallbacks = ['release']
        }
        vlcBundle {
            initWith release
            matchingFallbacks = ['release']
        }
        debug {
            applicationIdSuffix ".debug"
            jniDebuggable true
        }
        dev {
            initWith debug
            matchingFallbacks = ['debug']
        }
    }

    sourceSets.release {
        manifest.srcFile 'flavors/release/AndroidManifest.xml'
    }
    sourceSets.signedRelease {
        manifest.srcFile 'flavors/release/AndroidManifest.xml'
    }

    bundle {
        language {
            enableSplit = false
        }
    }

    splits {
        abi {
            def isReleaseBuild = false
            gradle.startParameter.taskNames.find {
                // Enable split for release builds in different build flavors
                // (assemblePaidRelease, assembleFreeRelease, etc.).
                if (it.toLowerCase() ==~ /assemble.*Release/.toLowerCase()) {
                    isReleaseBuild = true
                    return true // break
                }
                return false // continue
            }
            enable isReleaseBuild
            reset()
            include "x86", "x86_64", "armeabi-v7a", "arm64-v8a"
//            universalApk !isReleaseBuild
        }
    }

    def abiCodes = ['armeabi-v7a': 5, 'arm64-v8a': 6, 'x86': 7, 'x86_64': 8]
    lint {
        abortOnError false
        disable 'MissingTranslation', 'ExtraTranslation'
    }
    namespace 'org.videolan.mobile.app'
    // make per-variant version code
    applicationVariants.all { variant ->
        //Custom APK name and versionCode
        variant.outputs.each { output ->
            def outputAbi = output.getFilter(com.android.build.OutputFile.ABI)
            def abiName = System.getenv('GRADLE_ABI')?.toLowerCase() ?: "all"
            if (outputAbi != null) abiName = outputAbi
            def versionCode = abiCodes.get(abiName, 0)
            if (variant.buildType.name == "vlcBundle") {
                versionCode = 9
            }

            output.versionCodeOverride = 10000000 + defaultConfig.versionCode + versionCode
            def outputName = "VLC-Android-"
            outputName += variant.versionName.replace(" ", "-")
            if (variant.buildType.name == "debug") outputName += "-debug"
            outputName += "-" + abiName + ".apk"
            output.outputFileName = outputName
        }
    }

}

dependencies {
    implementation project(':application:vlc-android')
    implementation project(':application:television')
    implementation project(':application:remote-access-server')
    testImplementation project(':application:television')
    testImplementation project(':application:remote-access-server')

    androidTestImplementation "androidx.test.espresso:espresso-contrib:$rootProject.espressoVersion"
    androidTestImplementation "androidx.test.espresso:espresso-core:$rootProject.espressoVersion"
    androidTestImplementation "androidx.test.espresso:espresso-intents:$rootProject.espressoVersion"
    testImplementation "junit:junit:$rootProject.ext.junitVersion"
    androidTestImplementation "androidx.room:room-testing:$rootProject.ext.roomVersion"
    testImplementation "androidx.arch.core:core-testing:$rootProject.ext.archVersion"
    androidTestImplementation "androidx.arch.core:core-testing:$rootProject.ext.archVersion"
    androidTestImplementation "androidx.test.ext:junit:$rootProject.ext.junitExtVersion"
    coreLibraryDesugaring "com.android.tools:desugar_jdk_libs:$rootProject.ext.desugarLib"
    androidTestUtil "androidx.test:orchestrator:$rootProject.ext.orchestrator"
    testImplementation "androidx.test:core:$rootProject.ext.testCore"
    testImplementation "org.jetbrains.kotlinx:kotlinx-coroutines-test:$rootProject.ext.kotlinx_version"
    testImplementation "org.mockito:mockito-core:$rootProject.ext.mockito"
    testImplementation "io.mockk:mockk:$rootProject.ext.mockk"
    testImplementation "org.powermock:powermock-api-mockito2:$rootProject.ext.powerMock"
    testImplementation "org.powermock:powermock-module-junit4:$rootProject.ext.powerMock"
    testImplementation "com.jraska.livedata:testing-ktx:$rootProject.ext.livedataTest"
    testImplementation "org.robolectric:robolectric:$rootProject.ext.robolectric"
    androidTestImplementation "com.android.support:multidex:1.0.3"
    androidTestImplementation "androidx.test.uiautomator:uiautomator:2.2.0"
    androidTestImplementation "androidx.test:rules:$rootProject.ext.testCore"
    androidTestImplementation 'com.jraska:falcon:2.2.0'
    androidTestImplementation 'tools.fastlane:screengrab:2.1.0'
}
