<!--- 
/!\ Read this first!/!\  

This default template purpose is to be used to report a bug you found in VLC for Android.

To use a different template, select it from the "Description" drop-down above. If you're having trouble finding it, see https://code.videolan.org/videolan/vlc-android/-/wikis/Create-an-issue-and-use-a-template.

If you want to ask for a new feature, please use the "VLC for Android - Feature Request.md" template above.

If you're a libvlc java developer, please use the "libvlc - Bug" template.

If you just want to ask questions on how to use VLC for Android, please use our forum at https://forum.videolan.org/viewforum.php?f=35

Please note that any ticket not using a template may be closed without notice as it won't provide the necessary information.

-->



<!--- Provide a general summary of the issue in the Title above -->

## Description

<!--- Describe your bug in detail -->

#### Expected behavior

#### Actual behavior

#### Steps to reproduce

1.
2.
3.

#### Screenshot / video

<!--Add a screenshot or screencast when applicable-->
<!--To take a screenshot, see https://support.google.com/android/answer/9075928?hl=en-->


## Context

#### App version

<!--You can find it in the About screen of the app-->

#### Android version

#### Device model

#### App mode

<!--Remove the useless modes-->
**Smartphone**

**TV**

**Auto**